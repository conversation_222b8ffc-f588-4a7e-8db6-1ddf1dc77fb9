import dayjs from "dayjs";

export const dataConversion = value => {
  // 如果是 null 或 undefined，返回空数组
  if (!value) {
    return [];
  }

  // 如果已经是数组，直接返回
  if (Array.isArray(value)) {
    return value;
  }

  // 如果是对象，包装成数组
  if (typeof value === "object") {
    return [value];
  }

  // 其他情况（字符串、数字等），包装成数组
  return [value];
};

/**
 * 根据当前时间返回对应的时段标识
 * @returns {string} 时段标识：'1' 表示早上（6:00-11:59），'2' 表示下午（12:00-18:00），其余时间返回空字符串
 */
export const getTimePeriod = () => {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();

  // 将时间转换为分钟总数，便于比较
  const totalMinutes = hours * 60 + minutes;

  // 定义时段边界（分钟表示）
  const morningStart = 6 * 60; // 6:00
  const morningEnd = 11 * 60 + 59; // 11:59
  const afternoonStart = 12 * 60; // 12:00
  const afternoonEnd = 18 * 60; // 18:00

  if (totalMinutes >= morningStart && totalMinutes <= morningEnd) {
    return { value: "1", label: "上午" };
  } else if (totalMinutes >= afternoonStart && totalMinutes <= afternoonEnd) {
    return { value: "2", label: "下午" };
  } else {
    return { value: "", label: "" };
  }
};

/**
 * 获取当天星期几
 * @returns {string} 例如 星期一
 */
export const getChineseWeekday = () => {
  const weekdays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
  return weekdays[dayjs().day()];
};


/**
 * 休眠函数
 * @param milliseconds 休眠时间(毫秒)
 * @returns {Promise<unknown>}
 */
export function sleep(milliseconds) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, milliseconds);
  });
}