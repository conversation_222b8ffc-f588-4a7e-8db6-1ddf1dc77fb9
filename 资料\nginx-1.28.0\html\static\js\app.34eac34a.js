(function(e){function t(t){for(var o,n,c=t[0],u=t[1],d=t[2],i=0,l=[];i<c.length;i++)n=c[i],Object.prototype.hasOwnProperty.call(s,n)&&s[n]&&l.push(s[n][0]),s[n]=0;for(o in u)Object.prototype.hasOwnProperty.call(u,o)&&(e[o]=u[o]);p&&p(t);while(l.length)l.shift()();return a.push.apply(a,d||[]),r()}function r(){for(var e,t=0;t<a.length;t++){for(var r=a[t],o=!0,n=1;n<r.length;n++){var c=r[n];0!==s[c]&&(o=!1)}o&&(a.splice(t--,1),e=u(u.s=r[0]))}return e}var o={},n={app:0},s={app:0},a=[];function c(e){return u.p+"static/js/"+({}[e]||e)+"."+{"chunk-1eba399a":"ca747a95","chunk-47c26b78":"67817a7a"}[e]+".js"}function u(t){if(o[t])return o[t].exports;var r=o[t]={i:t,l:!1,exports:{}};return e[t].call(r.exports,r,r.exports,u),r.l=!0,r.exports}u.e=function(e){var t=[],r={"chunk-1eba399a":1,"chunk-47c26b78":1};n[e]?t.push(n[e]):0!==n[e]&&r[e]&&t.push(n[e]=new Promise((function(t,r){for(var o="static/css/"+({}[e]||e)+"."+{"chunk-1eba399a":"9a70c0a1","chunk-47c26b78":"ab06f376"}[e]+".css",s=u.p+o,a=document.getElementsByTagName("link"),c=0;c<a.length;c++){var d=a[c],i=d.getAttribute("data-href")||d.getAttribute("href");if("stylesheet"===d.rel&&(i===o||i===s))return t()}var l=document.getElementsByTagName("style");for(c=0;c<l.length;c++){d=l[c],i=d.getAttribute("data-href");if(i===o||i===s)return t()}var p=document.createElement("link");p.rel="stylesheet",p.type="text/css",p.onload=t,p.onerror=function(t){var o=t&&t.target&&t.target.src||s,a=new Error("Loading CSS chunk "+e+" failed.\n("+o+")");a.code="CSS_CHUNK_LOAD_FAILED",a.request=o,delete n[e],p.parentNode.removeChild(p),r(a)},p.href=s;var m=document.getElementsByTagName("head")[0];m.appendChild(p)})).then((function(){n[e]=0})));var o=s[e];if(0!==o)if(o)t.push(o[2]);else{var a=new Promise((function(t,r){o=s[e]=[t,r]}));t.push(o[2]=a);var d,i=document.createElement("script");i.charset="utf-8",i.timeout=120,u.nc&&i.setAttribute("nonce",u.nc),i.src=c(e);var l=new Error;d=function(t){i.onerror=i.onload=null,clearTimeout(p);var r=s[e];if(0!==r){if(r){var o=t&&("load"===t.type?"missing":t.type),n=t&&t.target&&t.target.src;l.message="Loading chunk "+e+" failed.\n("+o+": "+n+")",l.name="ChunkLoadError",l.type=o,l.request=n,r[1](l)}s[e]=void 0}};var p=setTimeout((function(){d({type:"timeout",target:i})}),12e4);i.onerror=i.onload=d,document.head.appendChild(i)}return Promise.all(t)},u.m=e,u.c=o,u.d=function(e,t,r){u.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},u.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},u.t=function(e,t){if(1&t&&(e=u(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(u.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)u.d(r,o,function(t){return e[t]}.bind(null,o));return r},u.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return u.d(t,"a",t),t},u.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},u.p="/",u.oe=function(e){throw console.error(e),e};var d=window["webpackJsonp"]=window["webpackJsonp"]||[],i=d.push.bind(d);d.push=t,d=d.slice();for(var l=0;l<d.length;l++)t(d[l]);var p=i;a.push([0,"chunk-vendors"]),r()})({0:function(e,t,r){e.exports=r("56d7")},1:function(e,t){},2:function(e,t){},"2f89":function(e,t,r){},3:function(e,t){},"56d7":function(e,t,r){"use strict";r.r(t);var o=r("2b0e"),n=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[t("router-view")],1)},s=[],a=r("2877"),c={},u=Object(a["a"])(c,n,s,!1,null,null,null),d=u.exports,i=r("8c4f"),l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"home"},[t("div",{staticClass:"navigation"},[t("router-link",{staticClass:"nav-button",attrs:{to:"/priceScreen"}},[t("div",{staticClass:"button-content"},[t("h3",[e._v("物价屏")])])]),t("router-link",{staticClass:"nav-button",attrs:{to:"/specialistScreen"}},[t("div",{staticClass:"button-content"},[t("h3",[e._v("专家出诊屏")])])]),t("router-link",{staticClass:"nav-button",attrs:{to:"/promotionalScreen"}},[t("div",{staticClass:"button-content"},[t("h3",[e._v("宣传屏")])])])],1)])},p=[],m={name:"Home"},h=m,f=(r("cef1"),Object(a["a"])(h,l,p,!1,null,"a807638e",null)),g=f.exports;o["default"].use(i["a"]);const C=[{path:"/",name:"Home",component:g},{path:"/priceScreen",name:"priceScreen",component:()=>r.e("chunk-47c26b78").then(r.bind(null,"bf71"))},{path:"/specialistScreen",name:"specialistScreen",component:()=>r.e("chunk-1eba399a").then(r.bind(null,"0b9c"))},{path:"/promotionalScreen",name:"promotionalScreen",component:()=>r.e("chunk-1eba399a").then(r.bind(null,"0b9c"))}],S=new i["a"]({mode:"history",base:"/",routes:C});var y=S,v=r("8750"),D=(r("f5df"),r("b20f"),r("5c96")),b=r.n(D),E=(r("0fae"),r("cee4"));const T="/api",w={development:{baseURL:T+"/csp/hsb/DHC.Published.ServiceForHATM.BS.ServiceForHATM.CLS",timeout:3e4,rejectUnauthorized:!1},production:{baseURL:T+"/csp/hsb/DHC.Published.ServiceForHATM.BS.ServiceForHATM.CLS",timeout:15e3,rejectUnauthorized:!1}},A="production",O=w[A],I={headers:{"Content-Type":"text/xml; charset=utf-8",SOAPAction:"http://www.dhcc.com.cn/DHC.Published.ServiceForHATM.BS.ServiceForHATM.HIPMessageServer"},namespace:"http://www.dhcc.com.cn",envelope:{soap:"http://schemas.xmlsoap.org/soap/envelope/"}},R={QUERY_DEPARTMENT:"MES0018",QUERY_DOCTOR:"MES0038",QUERY_SCHEDULE:"MES0039",QUERY_TIME_INFO:"MES0040",QUERY_PRICE:"MES0061"};const $=E["a"].create({baseURL:O.baseURL,timeout:O.timeout,headers:I.headers,httpsAgent:new(r("24f8").Agent)({rejectUnauthorized:O.rejectUnauthorized})});function x(e,t){return`<soap:Envelope xmlns:soap="${I.envelope.soap}">\n    <soap:Body>\n        <HIPMessageServer xmlns="${I.namespace}">\n            <action>${e}</action>\n            <message>\n                <![CDATA[${t}]]>\n            </message>\n        </HIPMessageServer>\n    </soap:Body>\n</soap:Envelope>`}function U(e){try{const r=e.match(/<!\[CDATA\[(.*?)\]\]>/s);if(r&&r[1]){const e=r[1].trim();if(e.startsWith("<"))return P(e);try{return JSON.parse(e)}catch(t){return{data:e}}}return{rawResponse:e}}catch(r){return console.error("解析SOAP响应失败:",r),{error:"响应解析失败",rawResponse:e}}}function P(e){const t={},r=/<(\w+)>([^<]*)<\/\1>/g;let o;while(null!==(o=r.exec(e))){const[,e,r]=o;t[e]=r.trim()}return Object.keys(t).length>0?t:{data:e}}async function M(e,t,r={}){try{console.log("发送SOAP请求 - Action: "+e),console.log("请求消息:",t);const o=x(e,t).trim(),n=await $.post("",o,{...r,headers:{...I.headers,...r.headers}});console.log("SOAP响应状态:",n.status),console.log("SOAP响应数据:",n.data);const s=U(n.data);return{success:!0,data:s,status:n.status,headers:n.headers}}catch(o){return console.error("SOAP请求失败:",o),o.response?{success:!1,error:"服务器响应错误",status:o.response.status,data:o.response.data,message:o.message}:o.request?{success:!1,error:"网络连接错误",message:"无法连接到服务器，请检查网络连接"}:{success:!1,error:"请求配置错误",message:o.message}}}$.interceptors.request.use(e=>(console.log("发送SOAP请求:",e.url),e),e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),$.interceptors.response.use(e=>(console.log("收到SOAP响应:",e.status),e),e=>(console.error("响应拦截器错误:",e),Promise.reject(e)));var _=$;async function q(e={}){try{const{hospitalId:t="SGSDYRMYY",extUserId:r="NOVA001",extOrgCode:o="",clientType:n="",departmentType:s="",departmentCode:a="",departmentGroupCode:c=""}=e,u=`<Request>\n  <TradeCode>1012</TradeCode>\n  <ExtOrgCode>${o}</ExtOrgCode>\n  <ClientType>${n}</ClientType>\n  <HospitalId>${t}</HospitalId>\n  <DepartmentType>${s}</DepartmentType>\n  <DepartmentCode>${a}</DepartmentCode>\n  <DepartmentGroupCode>${c}</DepartmentGroupCode>\n  <ExtUserID>${r}</ExtUserID>\n</Request>`;console.log("科室查询请求参数:",e);const d=await M(R.QUERY_DEPARTMENT,u);return d.success?(console.log("科室查询成功:",d.data),{success:!0,data:d.data,message:"科室查询成功"}):(console.error("科室查询失败:",d.error),{success:!1,error:d.error,message:d.message||"科室查询失败"})}catch(t){return console.error("科室查询异常:",t),{success:!1,error:"查询异常",message:t.message||"科室查询过程中发生异常"}}}async function H(){return await q()}async function k(e){return await q({departmentType:e})}async function Y(e){return await q({departmentGroupCode:e})}var j={queryDepartment:q,getAllDepartments:H,queryDepartmentByType:k,querySubDepartments:Y};async function B(e={}){try{const{hospitalId:t="SGSDYRMYY",extUserId:r="NOVA001",departmentCode:o,extOrgCode:n="",clientType:s="",doctorCode:a=""}=e;if(!o)return{success:!1,error:"参数错误",message:"科室代码不能为空"};const c=`<Request>\n  <TradeCode>1013</TradeCode>\n  <ExtOrgCode>${n}</ExtOrgCode>\n  <ClientType>${s}</ClientType>\n  <HospitalId>${t}</HospitalId>\n  <ExtUserID>${r}</ExtUserID>\n  <DepartmentCode>${o}</DepartmentCode>\n  <DoctorCode>${a}</DoctorCode>\n</Request>`;console.log("医生查询请求参数:",e);const u=await M(R.QUERY_DOCTOR,c);return u.success?(console.log("医生查询成功:",u.data),{success:!0,data:u.data,message:"医生查询成功"}):(console.error("医生查询失败:",u.error),{success:!1,error:u.error,message:u.message||"医生查询失败"})}catch(t){return console.error("医生查询异常:",t),{success:!1,error:"查询异常",message:t.message||"医生查询过程中发生异常"}}}async function N(e){return await B({departmentCode:e})}async function L(e,t){return await B({departmentCode:e,doctorCode:t})}var F={queryDoctor:B,queryDoctorsByDepartment:N,queryDoctorInfo:L};async function Q(e={}){try{const{hospitalId:t="SGSDYRMYY",extUserId:r="NOVA001",startDate:o,endDate:n,departmentCode:s,doctorCode:a="",stopScheduleFlag:c="N",rbasSessionCode:u=""}=e;if(!o||!n||!s)return{success:!1,error:"参数错误",message:"开始日期、结束日期和科室代码不能为空"};const d=`<Request>\n  <HospitalId>${t}</HospitalId>\n  <ExtUserID>${r}</ExtUserID>\n  <StartDate>${o}</StartDate>\n  <EndDate>${n}</EndDate>\n  <DepartmentCode>${s}</DepartmentCode>\n  <DoctorCode>${a}</DoctorCode>\n  <StopScheduleFlag>${c}</StopScheduleFlag>\n  <RBASSessionCode>${u}</RBASSessionCode>\n  <TradeCode>1004</TradeCode>\n</Request>`;console.log("排班查询请求参数:",e);const i=await M(R.QUERY_SCHEDULE,d);return i.success?(console.log("排班查询成功:",i.data),{success:!0,data:i.data,message:"排班查询成功"}):(console.error("排班查询失败:",i.error),{success:!1,error:i.error,message:i.message||"排班查询失败"})}catch(t){return console.error("排班查询异常:",t),{success:!1,error:"查询异常",message:t.message||"排班查询过程中发生异常"}}}async function G(e={}){try{const{hospitalId:t="SGSDYRMYY",extUserId:r="NOVA001",departmentCode:o,doctorCode:n,serviceDate:s,rbasSessionCode:a="",scheduleItemCode:c=""}=e;if(!o||!n||!s)return{success:!1,error:"参数错误",message:"科室代码、医生代码和出诊日期不能为空"};const u=`<Request>\n  <TradeCode>10041</TradeCode>\n  <HospitalId>${t}</HospitalId>\n  <ExtUserID>${r}</ExtUserID>\n  <DepartmentCode>${o}</DepartmentCode>\n  <DoctorCode>${n}</DoctorCode>\n  <RBASSessionCode>${a}</RBASSessionCode>\n  <ScheduleItemCode>${c}</ScheduleItemCode>\n  <ServiceDate>${s}</ServiceDate>\n</Request>`;console.log("分时信息查询请求参数:",e);const d=await M(R.QUERY_TIME_INFO,u);return d.success?(console.log("分时信息查询成功:",d.data),{success:!0,data:d.data,message:"分时信息查询成功"}):(console.error("分时信息查询失败:",d.error),{success:!1,error:d.error,message:d.message||"分时信息查询失败"})}catch(t){return console.error("分时信息查询异常:",t),{success:!1,error:"查询异常",message:t.message||"分时信息查询过程中发生异常"}}}async function z(e,t=""){const r=(new Date).toISOString().split("T")[0];return await Q({startDate:r,endDate:r,departmentCode:e,doctorCode:t})}async function V(e,t,r=""){return await Q({startDate:e,endDate:e,departmentCode:t,doctorCode:r})}var J={querySchedule:Q,queryScheduleTimeInfo:G,queryTodaySchedule:z,queryScheduleByDate:V};async function K(e={}){try{const{alias:t="",tradeCode:r="9100"}=e,o=`<Request><Alias>${t}</Alias><TradeCode>${r}</TradeCode></Request>`;console.log("物价查询请求参数:",e);const n=await M("MES0061",o);return n.success?(console.log("物价查询成功:",n.data),{success:!0,data:n.data,message:"物价查询成功"}):(console.error("物价查询失败:",n.error),{success:!1,error:n.error,message:n.message||"物价查询失败"})}catch(t){return console.error("物价查询异常:",t),{success:!1,error:"查询异常",message:t.message||"物价查询过程中发生异常"}}}var W={queryPrice:K};const X={soap:{client:_,sendRequest:M},department:j,doctor:F,schedule:J,price:W};o["default"].config.productionTip=!1,o["default"].prototype.$api=X,o["default"].use(b.a),new o["default"]({router:y,render:e=>e(d)}).$mount("#app"),v["a"].init({dh:1080,dw:1920,el:"#app",resize:!0})},b20f:function(e,t,r){},cef1:function(e,t,r){"use strict";r("2f89")}});