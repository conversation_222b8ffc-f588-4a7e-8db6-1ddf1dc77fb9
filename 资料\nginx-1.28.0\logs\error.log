2025/08/22 16:16:41 [error] 14340#14952: *1 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost", referrer: "http://localhost/"
2025/08/22 16:17:48 [error] 14340#14952: *4 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "127.0.0.1", referrer: "http://127.0.0.1/"
2025/08/22 16:34:22 [notice] 25880#24856: signal process started
2025/08/22 16:38:28 [notice] 27232#2996: signal process started
2025/08/22 16:38:28 [error] 27232#2996: OpenEvent("Global\ngx_reload_27864") failed (5: Access is denied)
2025/08/22 16:38:28 [notice] 27712#23648: signal process started
2025/08/22 16:38:28 [error] 27712#23648: OpenEvent("Global\ngx_quit_27864") failed (5: Access is denied)
2025/08/22 16:38:33 [error] 27956#28512: *1 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/favicon.ico" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /favicon.ico HTTP/1.1", host: "************:4000", referrer: "http://************:4000/"
2025/08/22 16:39:00 [error] 27956#28512: *3 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/favicon.ico" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /favicon.ico HTTP/1.1", host: "************:4000", referrer: "http://************:4000/"
2025/08/22 16:47:15 [notice] 1980#12568: signal process started
2025/08/22 16:47:15 [notice] 14004#11884: signal process started
2025/08/22 16:47:32 [notice] 29176#29136: signal process started
2025/08/22 16:47:32 [error] 29176#29136: CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/logs/nginx.pid" failed (2: The system cannot find the file specified)
2025/08/22 16:48:43 [notice] 7656#28520: signal process started
2025/08/22 16:48:43 [error] 7656#28520: CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/logs/nginx.pid" failed (2: The system cannot find the file specified)
2025/08/22 16:49:55 [notice] 9652#7552: signal process started
2025/08/22 16:49:55 [error] 9652#7552: CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/logs/nginx.pid" failed (2: The system cannot find the file specified)
2025/08/22 16:49:55 [notice] 20224#4908: signal process started
2025/08/22 16:49:55 [error] 20224#4908: CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/logs/nginx.pid" failed (2: The system cannot find the file specified)
2025/08/22 16:49:59 [notice] 2200#29552: signal process started
2025/08/22 16:51:29 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 16:51:31 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 16:51:31 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 16:51:31 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 16:51:31 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 16:51:31 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 16:51:32 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 16:51:32 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 16:51:32 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 16:51:32 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 16:51:33 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 16:51:33 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 16:51:33 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 16:51:33 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 16:51:33 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 16:51:33 [error] 16832#8660: *8 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 17:12:45 [notice] 28064#14820: signal process started
2025/08/22 17:13:06 [error] 8148#21296: *2 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 17:13:07 [error] 8148#21296: *2 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 17:13:07 [error] 8148#21296: *2 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 17:13:07 [error] 8148#21296: *2 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 17:13:07 [error] 8148#21296: *2 CreateFile() "C:\Users\<USER>\Desktop\nginx-1.28.0/html/priceScreen" failed (2: The system cannot find the file specified), client: ************, server: _, request: "GET /priceScreen HTTP/1.1", host: "************:4000"
2025/08/22 17:15:03 [notice] 28996#21608: signal process started
