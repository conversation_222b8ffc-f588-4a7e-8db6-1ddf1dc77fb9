import { sendSoapRequest } from "./request.js";
import dayjs from 'dayjs';

/**
 * 排班相关API
 * 根据接口文档实现排班查询功能
 */

/**
 * 查询排班记录 (1004)
 * @param {Object} params - 查询参数
 * @param {string} params.hospitalId - 医院唯一编号 (默认: SGSDYRMYY)
 * @param {string} params.extUserId - 操作员代码 (默认: NOVA001)
 * @param {string} params.startDate - 开始日期 (YYYY-MM-DD) (默认当天)
 * @param {string} params.endDate - 结束日期 (YYYY-MM-DD) (默认当天)
 * @param {string} params.departmentCode - 科室代码 (不填查全部)
 * @param {string} params.doctorCode - 医生代码 (可选)
 * @param {string} params.stopScheduleFlag - 查询排班标记 (默认: N) N:正常的排班    S:停诊的排班
 * @param {string} params.rbasSessionCode - 出诊时段代码 (可选)
 * @returns {Promise<Object>} 排班查询结果
 */
export async function querySchedule(params = {}) {
  try {
    // YYYY-MM-DD格式
    const today = dayjs().format('YYYY-MM-DD');

    const {
      hospitalId = "SGSDYRMYY",
      extUserId = "NOVA001",
      startDate = today,
      endDate = today,
      departmentCode = "",
      doctorCode = "",
      stopScheduleFlag = "N",
      rbasSessionCode = "",
    } = params;

    // 构建请求消息XML
    const requestMessage = `<Request>
  <HospitalId>${hospitalId}</HospitalId>
  <ExtUserID>${extUserId}</ExtUserID>
  <StartDate>${startDate}</StartDate>
  <EndDate>${endDate}</EndDate>
  <DepartmentCode>${departmentCode}</DepartmentCode>
  <DoctorCode>${doctorCode}</DoctorCode>
  <StopScheduleFlag>${stopScheduleFlag}</StopScheduleFlag>
  <RBASSessionCode>${rbasSessionCode}</RBASSessionCode>
  <TradeCode>1004</TradeCode>
</Request>`;

    console.log("排班查询请求参数:", params);

    // 发送SOAP请求
    const result = await sendSoapRequest("MES0039", requestMessage);

    if (result.success) {
      console.log("排班查询成功:", result.data);
      
      const responseData = result.data.Response;
      return {
        success: true,
        data: responseData,
        message: "排班查询成功",
      };
    } else {
      console.error("排班查询失败:", result.error);
      return {
        success: false,
        error: result.error,
        message: result.message || "排班查询失败",
      };
    }
  } catch (error) {
    console.error("排班查询异常:", error);
    return {
      success: false,
      error: "查询异常",
      message: error.message || "排班查询过程中发生异常",
    };
  }
}

// 导出所有API方法
export default {
  querySchedule,
};
