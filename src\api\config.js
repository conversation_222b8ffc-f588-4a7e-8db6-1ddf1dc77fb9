/**
 * API配置文件
 * 统一管理所有API相关的配置参数
 */

// 环境配置
const prefix = "/api";
// const prefix = 'https://172.16.8.4:1443'

const ENV_CONFIG = {
  development: {
    baseURL: `${prefix}/csp/hsb/DHC.Published.ServiceForHATM.BS.ServiceForHATM.CLS`,
    timeout: 30000,
    rejectUnauthorized: false, // 忽略SSL证书
  },
  production: {
    baseURL: `${prefix}/csp/hsb/DHC.Published.ServiceForHATM.BS.ServiceForHATM.CLS`,
    timeout: 15000,
    // rejectUnauthorized: true, // 验证SSL证书
    rejectUnauthorized: false, // 忽略SSL证书
  },
};

// 获取当前环境配置
const currentEnv = process.env.NODE_ENV || "development";
export const API_CONFIG = ENV_CONFIG[currentEnv];

// SOAP请求配置
export const SOAP_CONFIG = {
  headers: {
    "Content-Type": "text/xml; charset=utf-8",
    SOAPAction:
      "http://www.dhcc.com.cn/DHC.Published.ServiceForHATM.BS.ServiceForHATM.HIPMessageServer",
  },
  namespace: "http://www.dhcc.com.cn",
  envelope: {
    soap: "http://schemas.xmlsoap.org/soap/envelope/",
  },
};

// 错误代码映射
export const ERROR_CODES = {
  0: "成功",
  1001: "参数错误",
  1002: "数据不存在",
  1003: "系统繁忙",
  1004: "权限不足",
  1005: "网络超时",
  9999: "未知错误",
};

// 导出所有配置
export default {
  API_CONFIG,
  SOAP_CONFIG,
  ERROR_CODES,
};
