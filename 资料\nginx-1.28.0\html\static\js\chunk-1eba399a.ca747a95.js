(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1eba399a"],{"0b9c":function(t,n,e){"use strict";e.r(n);var a=function(){var t=this,n=t._self._c;return n("div",{staticClass:"page-container"},[n("h1",[t._v("页面2")]),n("p",[t._v("这是页面2的内容，用于展示大屏数据可视化内容。")]),t._m(0),n("router-link",{staticClass:"back-btn",attrs:{to:"/"}},[t._v("返回首页")])],1)},c=[function(){var t=this,n=t._self._c;return n("div",{staticClass:"content-area"},[n("p",[t._v("在这里可以添加图表、数据展示等内容")])])}],s={name:"specialistScreen",data(){return{}}},i=s,r=(e("9e02"),e("2877")),u=Object(r["a"])(i,a,c,!1,null,"891c3678",null);n["default"]=u.exports},"9e02":function(t,n,e){"use strict";e("ec75")},ec75:function(t,n,e){}}]);