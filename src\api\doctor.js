import { sendSoapRequest } from './request.js'

/**
 * 医生相关API
 * 根据接口文档实现医生查询功能
 */

/**
 * 查询医生列表 (1013)
 * @param {Object} params - 查询参数
 * @param {string} params.hospitalId - 医院唯一编号 (默认: SGSDYRMYY)
 * @param {string} params.extUserId - 操作员代码 (默认: NOVA001)
 * @param {string} params.departmentCode - 科室代码 (必填)
 * @param {string} params.extOrgCode - 预约机构 (可选)
 * @param {string} params.clientType - 客户端类型 (可选)
 * @param {string} params.doctorCode - 医生代码 (可选)
 * @returns {Promise<Object>} 医生查询结果
 */
export async function queryDoctor(params = {}) {
  try {
    const {
      hospitalId = 'SGSDYRMYY',
      extUserId = 'NOVA001',
      departmentCode,
      extOrgCode = '',
      clientType = '',
      doctorCode = ''
    } = params

    // 科室代码是必填参数
    if (!departmentCode) {
      return {
        success: false,
        error: '参数错误',
        message: '科室代码不能为空'
      }
    }

    // 构建请求消息XML
    const requestMessage = `<Request>
  <TradeCode>1013</TradeCode>
  <ExtOrgCode>${extOrgCode}</ExtOrgCode>
  <ClientType>${clientType}</ClientType>
  <HospitalId>${hospitalId}</HospitalId>
  <ExtUserID>${extUserId}</ExtUserID>
  <DepartmentCode>${departmentCode}</DepartmentCode>
  <DoctorCode>${doctorCode}</DoctorCode>
</Request>`


    // 发送SOAP请求
    const result = await sendSoapRequest("MES0038", requestMessage)

    if (result.success) {
      console.log('医生查询成功:', result.data)
      const responseData = result.data.Response;
      return {
        success: true,
        data: responseData,
        message: '医生查询成功'
      }
    } else {
      console.error('医生查询失败:', result.error)
      return {
        success: false,
        error: result.error,
        message: result.message || '医生查询失败'
      }
    }
  } catch (error) {
    console.error('医生查询异常:', error)
    return {
      success: false,
      error: '查询异常',
      message: error.message || '医生查询过程中发生异常'
    }
  }
}


// 导出所有API方法
export default {
  queryDoctor,
}
