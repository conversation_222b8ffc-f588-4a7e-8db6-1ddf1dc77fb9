(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-47c26b78"],{"04f8":function(e,t,r){"use strict";var n=r("1212"),o=r("d039"),a=r("cfe9"),c=a.String;e.exports=!!Object.getOwnPropertySymbols&&!o((function(){var e=Symbol("symbol detection");return!c(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},"06cf":function(e,t,r){"use strict";var n=r("83ab"),o=r("c65b"),a=r("d1e7"),c=r("5c6c"),i=r("fc6a"),u=r("a04b"),s=r("1a2d"),m=r("0cfb"),f=Object.getOwnPropertyDescriptor;t.f=n?f:function(e,t){if(e=i(e),t=u(t),m)try{return f(e,t)}catch(r){}if(s(e,t))return c(!o(a.f,e,t),e[t])}},"07fa":function(e,t,r){"use strict";var n=r("50c4");e.exports=function(e){return n(e.length)}},"0cfb":function(e,t,r){"use strict";var n=r("83ab"),o=r("d039"),a=r("cc12");e.exports=!n&&!o((function(){return 7!==Object.defineProperty(a("div"),"a",{get:function(){return 7}}).a}))},"0d51":function(e,t,r){"use strict";var n=String;e.exports=function(e){try{return n(e)}catch(t){return"Object"}}},1212:function(e,t,r){"use strict";var n,o,a=r("cfe9"),c=r("b5db"),i=a.process,u=a.Deno,s=i&&i.versions||u&&u.version,m=s&&s.v8;m&&(n=m.split("."),o=n[0]>0&&n[0]<4?1:+(n[0]+n[1])),!o&&c&&(n=c.match(/Edge\/(\d+)/),(!n||n[1]>=74)&&(n=c.match(/Chrome\/(\d+)/),n&&(o=+n[1]))),e.exports=o},"13d2":function(e,t,r){"use strict";var n=r("e330"),o=r("d039"),a=r("1626"),c=r("1a2d"),i=r("83ab"),u=r("5e77").CONFIGURABLE,s=r("8925"),m=r("69f3"),f=m.enforce,l=m.get,p=String,d=Object.defineProperty,b=n("".slice),S=n("".replace),C=n([].join),g=i&&!o((function(){return 8!==d((function(){}),"length",{value:8}).length})),I=String(String).split("String"),h=e.exports=function(e,t,r){"Symbol("===b(p(t),0,7)&&(t="["+S(p(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!c(e,"name")||u&&e.name!==t)&&(i?d(e,"name",{value:t,configurable:!0}):e.name=t),g&&r&&c(r,"arity")&&e.length!==r.arity&&d(e,"length",{value:r.arity});try{r&&c(r,"constructor")&&r.constructor?i&&d(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(o){}var n=f(e);return c(n,"source")||(n.source=C(I,"string"==typeof t?t:"")),e};Function.prototype.toString=h((function(){return a(this)&&l(this).source||s(this)}),"toString")},"14d9":function(e,t,r){"use strict";var n=r("23e7"),o=r("7b0b"),a=r("07fa"),c=r("3a34"),i=r("3511"),u=r("d039"),s=u((function(){return 4294967297!==[].push.call({length:4294967296},1)})),m=function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}},f=s||!m();n({target:"Array",proto:!0,arity:1,forced:f},{push:function(e){var t=o(this),r=a(t),n=arguments.length;i(r+n);for(var u=0;u<n;u++)t[r]=arguments[u],r++;return c(t,r),r}})},1626:function(e,t,r){"use strict";var n="object"==typeof document&&document.all;e.exports="undefined"==typeof n&&void 0!==n?function(e){return"function"==typeof e||e===n}:function(e){return"function"==typeof e}},"198f":function(e,t,r){},"1a2d":function(e,t,r){"use strict";var n=r("e330"),o=r("7b0b"),a=n({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return a(o(e),t)}},"1d80":function(e,t,r){"use strict";var n=r("7234"),o=TypeError;e.exports=function(e){if(n(e))throw new o("Can't call method on "+e);return e}},"23cb":function(e,t,r){"use strict";var n=r("5926"),o=Math.max,a=Math.min;e.exports=function(e,t){var r=n(e);return r<0?o(r+t,0):a(r,t)}},"23e7":function(e,t,r){"use strict";var n=r("cfe9"),o=r("06cf").f,a=r("9112"),c=r("cb2d"),i=r("6374"),u=r("e893"),s=r("94ca");e.exports=function(e,t){var r,m,f,l,p,d,b=e.target,S=e.global,C=e.stat;if(m=S?n:C?n[b]||i(b,{}):n[b]&&n[b].prototype,m)for(f in t){if(p=t[f],e.dontCallGetSet?(d=o(m,f),l=d&&d.value):l=m[f],r=s(S?f:b+(C?".":"#")+f,e.forced),!r&&void 0!==l){if(typeof p==typeof l)continue;u(p,l)}(e.sham||l&&l.sham)&&a(p,"sham",!0),c(m,f,p,e)}}},"241c":function(e,t,r){"use strict";var n=r("ca84"),o=r("7839"),a=o.concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,a)}},3511:function(e,t,r){"use strict";var n=TypeError,o=9007199254740991;e.exports=function(e){if(e>o)throw n("Maximum allowed index exceeded");return e}},"3a34":function(e,t,r){"use strict";var n=r("83ab"),o=r("e8b5"),a=TypeError,c=Object.getOwnPropertyDescriptor,i=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=i?function(e,t){if(o(e)&&!c(e,"length").writable)throw new a("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},"3a9b":function(e,t,r){"use strict";var n=r("e330");e.exports=n({}.isPrototypeOf)},"40d5":function(e,t,r){"use strict";var n=r("d039");e.exports=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},"44ad":function(e,t,r){"use strict";var n=r("e330"),o=r("d039"),a=r("c6b6"),c=Object,i=n("".split);e.exports=o((function(){return!c("z").propertyIsEnumerable(0)}))?function(e){return"String"===a(e)?i(e,""):c(e)}:c},"485a":function(e,t,r){"use strict";var n=r("c65b"),o=r("1626"),a=r("861d"),c=TypeError;e.exports=function(e,t){var r,i;if("string"===t&&o(r=e.toString)&&!a(i=n(r,e)))return i;if(o(r=e.valueOf)&&!a(i=n(r,e)))return i;if("string"!==t&&o(r=e.toString)&&!a(i=n(r,e)))return i;throw new c("Can't convert object to primitive value")}},"4d64":function(e,t,r){"use strict";var n=r("fc6a"),o=r("23cb"),a=r("07fa"),c=function(e){return function(t,r,c){var i=n(t),u=a(i);if(0===u)return!e&&-1;var s,m=o(c,u);if(e&&r!==r){while(u>m)if(s=i[m++],s!==s)return!0}else for(;u>m;m++)if((e||m in i)&&i[m]===r)return e||m||0;return!e&&-1}};e.exports={includes:c(!0),indexOf:c(!1)}},"50c4":function(e,t,r){"use strict";var n=r("5926"),o=Math.min;e.exports=function(e){var t=n(e);return t>0?o(t,9007199254740991):0}},5692:function(e,t,r){"use strict";var n=r("c6cd");e.exports=function(e,t){return n[e]||(n[e]=t||{})}},"56ef":function(e,t,r){"use strict";var n=r("d066"),o=r("e330"),a=r("241c"),c=r("7418"),i=r("825a"),u=o([].concat);e.exports=n("Reflect","ownKeys")||function(e){var t=a.f(i(e)),r=c.f;return r?u(t,r(e)):t}},5926:function(e,t,r){"use strict";var n=r("b42e");e.exports=function(e){var t=+e;return t!==t||0===t?0:n(t)}},"59ed":function(e,t,r){"use strict";var n=r("1626"),o=r("0d51"),a=TypeError;e.exports=function(e){if(n(e))return e;throw new a(o(e)+" is not a function")}},"5c6c":function(e,t,r){"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},"5e77":function(e,t,r){"use strict";var n=r("83ab"),o=r("1a2d"),a=Function.prototype,c=n&&Object.getOwnPropertyDescriptor,i=o(a,"name"),u=i&&"something"===function(){}.name,s=i&&(!n||n&&c(a,"name").configurable);e.exports={EXISTS:i,PROPER:u,CONFIGURABLE:s}},6374:function(e,t,r){"use strict";var n=r("cfe9"),o=Object.defineProperty;e.exports=function(e,t){try{o(n,e,{value:t,configurable:!0,writable:!0})}catch(r){n[e]=t}return t}},"69f3":function(e,t,r){"use strict";var n,o,a,c=r("cdce"),i=r("cfe9"),u=r("861d"),s=r("9112"),m=r("1a2d"),f=r("c6cd"),l=r("f772"),p=r("d012"),d="Object already initialized",b=i.TypeError,S=i.WeakMap,C=function(e){return a(e)?o(e):n(e,{})},g=function(e){return function(t){var r;if(!u(t)||(r=o(t)).type!==e)throw new b("Incompatible receiver, "+e+" required");return r}};if(c||f.state){var I=f.state||(f.state=new S);I.get=I.get,I.has=I.has,I.set=I.set,n=function(e,t){if(I.has(e))throw new b(d);return t.facade=e,I.set(e,t),t},o=function(e){return I.get(e)||{}},a=function(e){return I.has(e)}}else{var h=l("state");p[h]=!0,n=function(e,t){if(m(e,h))throw new b(d);return t.facade=e,s(e,h,t),t},o=function(e){return m(e,h)?e[h]:{}},a=function(e){return m(e,h)}}e.exports={set:n,get:o,has:a,enforce:C,getterFor:g}},7234:function(e,t,r){"use strict";e.exports=function(e){return null===e||void 0===e}},7418:function(e,t,r){"use strict";t.f=Object.getOwnPropertySymbols},7839:function(e,t,r){"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},"7b0b":function(e,t,r){"use strict";var n=r("1d80"),o=Object;e.exports=function(e){return o(n(e))}},"825a":function(e,t,r){"use strict";var n=r("861d"),o=String,a=TypeError;e.exports=function(e){if(n(e))return e;throw new a(o(e)+" is not an object")}},"83ab":function(e,t,r){"use strict";var n=r("d039");e.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},"861d":function(e,t,r){"use strict";var n=r("1626");e.exports=function(e){return"object"==typeof e?null!==e:n(e)}},8639:function(e,t,r){e.exports=r.p+"static/media/price_screen_video.2f327022.mp4"},8925:function(e,t,r){"use strict";var n=r("e330"),o=r("1626"),a=r("c6cd"),c=n(Function.toString);o(a.inspectSource)||(a.inspectSource=function(e){return c(e)}),e.exports=a.inspectSource},"90e3":function(e,t,r){"use strict";var n=r("e330"),o=0,a=Math.random(),c=n(1.1.toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+c(++o+a,36)}},9112:function(e,t,r){"use strict";var n=r("83ab"),o=r("9bf2"),a=r("5c6c");e.exports=n?function(e,t,r){return o.f(e,t,a(1,r))}:function(e,t,r){return e[t]=r,e}},"94ca":function(e,t,r){"use strict";var n=r("d039"),o=r("1626"),a=/#|\.prototype\./,c=function(e,t){var r=u[i(e)];return r===m||r!==s&&(o(t)?n(t):!!t)},i=c.normalize=function(e){return String(e).replace(a,".").toLowerCase()},u=c.data={},s=c.NATIVE="N",m=c.POLYFILL="P";e.exports=c},"9bf2":function(e,t,r){"use strict";var n=r("83ab"),o=r("0cfb"),a=r("aed9"),c=r("825a"),i=r("a04b"),u=TypeError,s=Object.defineProperty,m=Object.getOwnPropertyDescriptor,f="enumerable",l="configurable",p="writable";t.f=n?a?function(e,t,r){if(c(e),t=i(t),c(r),"function"===typeof e&&"prototype"===t&&"value"in r&&p in r&&!r[p]){var n=m(e,t);n&&n[p]&&(e[t]=r.value,r={configurable:l in r?r[l]:n[l],enumerable:f in r?r[f]:n[f],writable:!1})}return s(e,t,r)}:s:function(e,t,r){if(c(e),t=i(t),c(r),o)try{return s(e,t,r)}catch(n){}if("get"in r||"set"in r)throw new u("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},a04b:function(e,t,r){"use strict";var n=r("c04e"),o=r("d9b5");e.exports=function(e){var t=n(e,"string");return o(t)?t:t+""}},aed9:function(e,t,r){"use strict";var n=r("83ab"),o=r("d039");e.exports=n&&o((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},b42e:function(e,t,r){"use strict";var n=Math.ceil,o=Math.floor;e.exports=Math.trunc||function(e){var t=+e;return(t>0?o:n)(t)}},b5db:function(e,t,r){"use strict";var n=r("cfe9"),o=n.navigator,a=o&&o.userAgent;e.exports=a?String(a):""},b622:function(e,t,r){"use strict";var n=r("cfe9"),o=r("5692"),a=r("1a2d"),c=r("90e3"),i=r("04f8"),u=r("fdbf"),s=n.Symbol,m=o("wks"),f=u?s["for"]||s:s&&s.withoutSetter||c;e.exports=function(e){return a(m,e)||(m[e]=i&&a(s,e)?s[e]:f("Symbol."+e)),m[e]}},bf71:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"price-screen-container"},[t("div",{staticClass:"table-section"},[e._m(0),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-wrapper"},[t("div",{ref:"scrollingTable",staticClass:"scrolling-table"},[t("el-table",{staticClass:"price-table",attrs:{data:e.priceData,stripe:"","show-header":!0,height:"100%"}},[t("el-table-column",{attrs:{prop:"ItemCode",label:"编码","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"name",label:"名称","min-width":"200",align:"center"}}),t("el-table-column",{attrs:{prop:"SpecInfo",label:"规格","min-width":"150",align:"center"}}),t("el-table-column",{attrs:{prop:"Uom",label:"单位","min-width":"80",align:"center"}}),t("el-table-column",{attrs:{prop:"TarSubCate",label:"类型","min-width":"120",align:"center"}}),t("el-table-column",{attrs:{prop:"Price",label:"单价(元)","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",{staticClass:"price-text"},[e._v(e._s(e.priceText(r.row.Price)))])]}}])})],1)],1)])]),t("div",{staticClass:"video-section"},[t("div",{staticClass:"video-wrapper"},[t("video",{ref:"priceVideo",staticClass:"price-video",attrs:{src:e.videoSrc,autoplay:"",loop:"",muted:""},domProps:{muted:!0},on:{error:e.handleVideoError,loadstart:e.handleVideoLoadStart,canplay:e.handleVideoCanPlay}},[e._v(" 您的浏览器不支持视频播放 ")]),e.videoError?t("div",{staticClass:"video-error"},[t("i",{staticClass:"el-icon-video-camera-solid"}),t("p",[e._v("视频加载失败")])]):e._e()])])])},o=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"table-header"},[t("h2",[e._v("商品价格信息")])])}];r("14d9");const a=(e,t=2)=>{if(!e&&0!==e)return"";"number"===typeof e&&(e=e.toString());let[r,n]=e.split(".");return n?(n=n.slice(0,t),e=`${r}.${n.padEnd(t,"0")}`):e=`${r}.${"".padEnd(t,"0")}`,e};var c=[{ItemCode:"MED001",name:"阿莫西林胶囊",SpecInfo:"0.25g*24粒",Uom:"盒",TarSubCate:"处方药",Price:""},{ItemCode:"MED002",name:"布洛芬缓释胶囊",SpecInfo:"0.3g*20粒",Uom:"盒",TarSubCate:"处方药",Price:"1"},{ItemCode:"MED003",name:"维生素C片",SpecInfo:"0.1g*100片",Uom:"瓶",TarSubCate:"非处方药",Price:"12.30"},{ItemCode:"MED004",name:"感冒灵颗粒",SpecInfo:"10g*9袋",Uom:"盒",TarSubCate:"非处方药",Price:"18.90"},{ItemCode:"MED005",name:"头孢克肟胶囊",SpecInfo:"0.1g*12粒",Uom:"盒",TarSubCate:"处方药",Price:"45.60"},{ItemCode:"MED006",name:"复方甘草片",SpecInfo:"50片",Uom:"瓶",TarSubCate:"处方药",Price:"8.70"},{ItemCode:"MED007",name:"氯雷他定片",SpecInfo:"10mg*7片",Uom:"盒",TarSubCate:"处方药",Price:"22.40"},{ItemCode:"MED008",name:"蒙脱石散",SpecInfo:"3g*10袋",Uom:"盒",TarSubCate:"非处方药",Price:"16.80"},{ItemCode:"MED009",name:"奥美拉唑肠溶胶囊",SpecInfo:"20mg*14粒",Uom:"盒",TarSubCate:"处方药",Price:"35.20"},{ItemCode:"MED010",name:"板蓝根颗粒",SpecInfo:"10g*20袋",Uom:"盒",TarSubCate:"非处方药",Price:"13.50"},{ItemCode:"MED011",name:"罗红霉素胶囊",SpecInfo:"150mg*12粒",Uom:"盒",TarSubCate:"处方药",Price:"26.70"},{ItemCode:"MED012",name:"双氯芬酸钠缓释片",SpecInfo:"75mg*10片",Uom:"盒",TarSubCate:"处方药",Price:"19.80"},{ItemCode:"MED013",name:"复合维生素B片",SpecInfo:"100片",Uom:"瓶",TarSubCate:"非处方药",Price:"14.60"},{ItemCode:"MED014",name:"硝苯地平缓释片",SpecInfo:"20mg*30片",Uom:"盒",TarSubCate:"处方药",Price:"42.30"},{ItemCode:"MED015",name:"藿香正气水",SpecInfo:"10ml*10支",Uom:"盒",TarSubCate:"非处方药",Price:"11.90"},{ItemCode:"MED016",name:"左氧氟沙星片",SpecInfo:"0.1g*10片",Uom:"盒",TarSubCate:"处方药",Price:"38.40"},{ItemCode:"MED017",name:"咳特灵胶囊",SpecInfo:"0.5g*24粒",Uom:"盒",TarSubCate:"非处方药",Price:"21.70"},{ItemCode:"MED018",name:"甲硝唑片",SpecInfo:"0.2g*100片",Uom:"瓶",TarSubCate:"处方药",Price:"9.80"},{ItemCode:"MED019",name:"健胃消食片",SpecInfo:"0.8g*36片",Uom:"盒",TarSubCate:"非处方药",Price:"17.20"},{ItemCode:"MED020",name:"阿司匹林肠溶片",SpecInfo:"25mg*30片",Uom:"盒",TarSubCate:"处方药",Price:"6.50"},{ItemCode:"MED021",name:"盐酸二甲双胍片",SpecInfo:"0.25g*48片",Uom:"盒",TarSubCate:"处方药",Price:"32.80"},{ItemCode:"MED022",name:"硫酸沙丁胺醇气雾剂",SpecInfo:"100μg*200揿",Uom:"支",TarSubCate:"处方药",Price:"24.60"},{ItemCode:"MED023",name:"复方氨酚烷胺片",SpecInfo:"12片",Uom:"盒",TarSubCate:"非处方药",Price:"9.50"},{ItemCode:"MED024",name:"马来酸氯苯那敏片",SpecInfo:"4mg*100片",Uom:"瓶",TarSubCate:"处方药",Price:"7.20"},{ItemCode:"MED025",name:"盐酸雷尼替丁胶囊",SpecInfo:"150mg*24粒",Uom:"盒",TarSubCate:"处方药",Price:"18.90"},{ItemCode:"MED026",name:"复方丹参滴丸",SpecInfo:"270丸",Uom:"瓶",TarSubCate:"非处方药",Price:"29.80"},{ItemCode:"MED027",name:"硝酸甘油片",SpecInfo:"0.5mg*100片",Uom:"瓶",TarSubCate:"处方药",Price:"12.40"},{ItemCode:"MED028",name:"盐酸氨溴索口服溶液",SpecInfo:"100ml",Uom:"瓶",TarSubCate:"非处方药",Price:"16.70"},{ItemCode:"MED029",name:"复方甲氧那明胶囊",SpecInfo:"24粒",Uom:"盒",TarSubCate:"处方药",Price:"33.50"},{ItemCode:"MED030",name:"葡萄糖酸钙口服溶液",SpecInfo:"10ml*10支",Uom:"盒",TarSubCate:"非处方药",Price:"8.90"},{ItemCode:"MED031",name:"盐酸左氧氟沙星胶囊",SpecInfo:"0.1g*6粒",Uom:"盒",TarSubCate:"处方药",Price:"25.60"},{ItemCode:"MED032",name:"复方感冒灵片",SpecInfo:"12片",Uom:"盒",TarSubCate:"非处方药",Price:"11.30"},{ItemCode:"MED033",name:"硫酸亚铁片",SpecInfo:"0.3g*100片",Uom:"瓶",TarSubCate:"非处方药",Price:"6.80"},{ItemCode:"MED034",name:"盐酸西替利嗪片",SpecInfo:"10mg*12片",Uom:"盒",TarSubCate:"处方药",Price:"19.40"},{ItemCode:"MED035",name:"复方桔梗片",SpecInfo:"24片",Uom:"盒",TarSubCate:"非处方药",Price:"13.70"},{ItemCode:"MED036",name:"硝苯地平片",SpecInfo:"10mg*100片",Uom:"瓶",TarSubCate:"处方药",Price:"15.20"},{ItemCode:"MED037",name:"盐酸多西环素片",SpecInfo:"0.1g*12片",Uom:"盒",TarSubCate:"处方药",Price:"22.90"},{ItemCode:"MED038",name:"复方氨基酸胶囊",SpecInfo:"0.25g*36粒",Uom:"盒",TarSubCate:"非处方药",Price:"28.40"},{ItemCode:"MED039",name:"马来酸依那普利片",SpecInfo:"10mg*28片",Uom:"盒",TarSubCate:"处方药",Price:"36.80"},{ItemCode:"MED040",name:"复方维生素片",SpecInfo:"60片",Uom:"瓶",TarSubCate:"非处方药",Price:"18.50"},{ItemCode:"MED041",name:"盐酸普萘洛尔片",SpecInfo:"10mg*100片",Uom:"瓶",TarSubCate:"处方药",Price:"9.60"},{ItemCode:"MED042",name:"复方新诺明片",SpecInfo:"12片",Uom:"盒",TarSubCate:"处方药",Price:"7.30"},{ItemCode:"MED043",name:"硫酸庆大霉素片",SpecInfo:"40mg*12片",Uom:"盒",TarSubCate:"处方药",Price:"14.80"},{ItemCode:"MED044",name:"复方氨酚咖敏片",SpecInfo:"12片",Uom:"盒",TarSubCate:"非处方药",Price:"10.90"},{ItemCode:"MED045",name:"盐酸氯丙嗪片",SpecInfo:"25mg*100片",Uom:"瓶",TarSubCate:"处方药",Price:"12.70"},{ItemCode:"MED046",name:"复方鱼腥草片",SpecInfo:"36片",Uom:"盒",TarSubCate:"非处方药",Price:"16.20"},{ItemCode:"MED047",name:"硝酸异山梨酯片",SpecInfo:"5mg*100片",Uom:"瓶",TarSubCate:"处方药",Price:"11.40"},{ItemCode:"MED048",name:"盐酸氨基葡萄糖胶囊",SpecInfo:"0.24g*20粒",Uom:"盒",TarSubCate:"非处方药",Price:"45.80"},{ItemCode:"MED049",name:"复方黄连素片",SpecInfo:"24片",Uom:"盒",TarSubCate:"非处方药",Price:"8.60"},{ItemCode:"MED050",name:"马来酸曲美布汀片",SpecInfo:"0.1g*30片",Uom:"盒",TarSubCate:"处方药",Price:"27.30"},{ItemCode:"MED051",name:"盐酸氟西汀胶囊",SpecInfo:"20mg*28粒",Uom:"盒",TarSubCate:"处方药",Price:"52.40"},{ItemCode:"MED052",name:"复方板蓝根颗粒",SpecInfo:"15g*9袋",Uom:"盒",TarSubCate:"非处方药",Price:"19.80"},{ItemCode:"MED053",name:"硫酸阿托品片",SpecInfo:"0.3mg*100片",Uom:"瓶",TarSubCate:"处方药",Price:"8.90"},{ItemCode:"MED054",name:"盐酸美托洛尔片",SpecInfo:"25mg*20片",Uom:"盒",TarSubCate:"处方药",Price:"15.60"},{ItemCode:"MED055",name:"复方穿心莲片",SpecInfo:"24片",Uom:"盒",TarSubCate:"非处方药",Price:"12.40"},{ItemCode:"MED056",name:"硝酸毛果芸香碱滴眼液",SpecInfo:"5ml",Uom:"支",TarSubCate:"处方药",Price:"23.70"},{ItemCode:"MED057",name:"盐酸地尔硫卓片",SpecInfo:"30mg*24片",Uom:"盒",TarSubCate:"处方药",Price:"18.90"},{ItemCode:"MED058",name:"复方甘草酸苷片",SpecInfo:"25mg*100片",Uom:"瓶",TarSubCate:"处方药",Price:"34.50"},{ItemCode:"MED059",name:"马来酸氯苯那敏注射液",SpecInfo:"1ml*10支",Uom:"盒",TarSubCate:"处方药",Price:"16.80"},{ItemCode:"MED060",name:"复方氨酚那敏片",SpecInfo:"12片",Uom:"盒",TarSubCate:"非处方药",Price:"9.70"},{ItemCode:"MED061",name:"盐酸克林霉素胶囊",SpecInfo:"150mg*12粒",Uom:"盒",TarSubCate:"处方药",Price:"21.30"},{ItemCode:"MED062",name:"硫酸特布他林片",SpecInfo:"2.5mg*20片",Uom:"盒",TarSubCate:"处方药",Price:"14.20"},{ItemCode:"MED063",name:"复方丹参片",SpecInfo:"60片",Uom:"瓶",TarSubCate:"非处方药",Price:"17.60"},{ItemCode:"MED064",name:"盐酸吗啉胍片",SpecInfo:"0.1g*100片",Uom:"瓶",TarSubCate:"处方药",Price:"11.80"},{ItemCode:"MED065",name:"复方氨酚愈敏口服溶液",SpecInfo:"120ml",Uom:"瓶",TarSubCate:"非处方药",Price:"22.90"},{ItemCode:"MED066",name:"硝酸咪康唑栓",SpecInfo:"0.2g*7粒",Uom:"盒",TarSubCate:"处方药",Price:"26.40"},{ItemCode:"MED067",name:"盐酸异丙嗪片",SpecInfo:"25mg*100片",Uom:"瓶",TarSubCate:"处方药",Price:"13.50"},{ItemCode:"MED068",name:"复方氨基比林注射液",SpecInfo:"2ml*10支",Uom:"盒",TarSubCate:"处方药",Price:"19.70"},{ItemCode:"MED069",name:"马来酸噻吗洛尔滴眼液",SpecInfo:"5ml",Uom:"支",TarSubCate:"处方药",Price:"31.20"},{ItemCode:"MED070",name:"复方薄荷脑滴鼻液",SpecInfo:"10ml",Uom:"瓶",TarSubCate:"非处方药",Price:"7.80"},{ItemCode:"MED071",name:"盐酸利多卡因注射液",SpecInfo:"5ml*10支",Uom:"盒",TarSubCate:"处方药",Price:"15.40"},{ItemCode:"MED072",name:"硫酸新霉素片",SpecInfo:"0.25g*12片",Uom:"盒",TarSubCate:"处方药",Price:"10.60"},{ItemCode:"MED073",name:"复方氨酚烷胺颗粒",SpecInfo:"6g*9袋",Uom:"盒",TarSubCate:"非处方药",Price:"14.30"},{ItemCode:"MED074",name:"盐酸苯海拉明片",SpecInfo:"25mg*100片",Uom:"瓶",TarSubCate:"处方药",Price:"8.70"},{ItemCode:"MED075",name:"复方磺胺甲噁唑片",SpecInfo:"0.48g*100片",Uom:"瓶",TarSubCate:"处方药",Price:"12.90"},{ItemCode:"MED076",name:"硝酸甘油气雾剂",SpecInfo:"4.9g",Uom:"瓶",TarSubCate:"处方药",Price:"28.60"},{ItemCode:"MED077",name:"盐酸氯胺酮注射液",SpecInfo:"2ml*10支",Uom:"盒",TarSubCate:"处方药",Price:"45.80"},{ItemCode:"MED078",name:"复方愈创木酚磺酸钾口服溶液",SpecInfo:"100ml",Uom:"瓶",TarSubCate:"非处方药",Price:"18.50"},{ItemCode:"MED079",name:"马来酸桂哌齐特片",SpecInfo:"80mg*20片",Uom:"盒",TarSubCate:"处方药",Price:"24.70"},{ItemCode:"MED080",name:"复方氨酚咖敏胶囊",SpecInfo:"12粒",Uom:"盒",TarSubCate:"非处方药",Price:"11.20"},{ItemCode:"MED081",name:"盐酸丁卡因胶浆",SpecInfo:"20g",Uom:"支",TarSubCate:"处方药",Price:"16.90"},{ItemCode:"MED082",name:"硫酸阿米卡星注射液",SpecInfo:"2ml*10支",Uom:"盒",TarSubCate:"处方药",Price:"32.40"},{ItemCode:"MED083",name:"复方氨酚那敏颗粒",SpecInfo:"6g*12袋",Uom:"盒",TarSubCate:"非处方药",Price:"13.80"},{ItemCode:"MED084",name:"盐酸哌替啶片",SpecInfo:"25mg*100片",Uom:"瓶",TarSubCate:"处方药",Price:"29.50"},{ItemCode:"MED085",name:"复方甘草合剂",SpecInfo:"100ml",Uom:"瓶",TarSubCate:"非处方药",Price:"9.40"},{ItemCode:"MED086",name:"硝酸益康唑栓",SpecInfo:"0.2g*6粒",Uom:"盒",TarSubCate:"处方药",Price:"22.80"},{ItemCode:"MED087",name:"盐酸氯丙嗪注射液",SpecInfo:"2ml*10支",Uom:"盒",TarSubCate:"处方药",Price:"17.60"},{ItemCode:"MED088",name:"复方氨酚愈敏片",SpecInfo:"12片",Uom:"盒",TarSubCate:"非处方药",Price:"10.30"},{ItemCode:"MED089",name:"马来酸氯苯那敏缓释片",SpecInfo:"8mg*10片",Uom:"盒",TarSubCate:"处方药",Price:"15.70"},{ItemCode:"MED090",name:"复方薄荷脑鼻用喷雾剂",SpecInfo:"20ml",Uom:"瓶",TarSubCate:"非处方药",Price:"12.60"},{ItemCode:"MED091",name:"盐酸普鲁卡因注射液",SpecInfo:"2ml*10支",Uom:"盒",TarSubCate:"处方药",Price:"8.90"},{ItemCode:"MED092",name:"硫酸链霉素注射液",SpecInfo:"1g*10支",Uom:"盒",TarSubCate:"处方药",Price:"25.40"},{ItemCode:"MED093",name:"复方氨酚烷胺口服溶液",SpecInfo:"60ml",Uom:"瓶",TarSubCate:"非处方药",Price:"16.80"},{ItemCode:"MED094",name:"盐酸赛庚啶片",SpecInfo:"2mg*100片",Uom:"瓶",TarSubCate:"处方药",Price:"11.50"},{ItemCode:"MED095",name:"复方磺胺嘧啶片",SpecInfo:"0.5g*100片",Uom:"瓶",TarSubCate:"处方药",Price:"14.20"},{ItemCode:"MED096",name:"硝酸甘油舌下片",SpecInfo:"0.5mg*100片",Uom:"瓶",TarSubCate:"处方药",Price:"9.80"},{ItemCode:"MED097",name:"盐酸氯胺酮片",SpecInfo:"50mg*20片",Uom:"盒",TarSubCate:"处方药",Price:"38.60"},{ItemCode:"MED098",name:"复方愈创甘油醚口服溶液",SpecInfo:"100ml",Uom:"瓶",TarSubCate:"非处方药",Price:"19.30"},{ItemCode:"MED099",name:"马来酸氯苯那敏糖浆",SpecInfo:"100ml",Uom:"瓶",TarSubCate:"处方药",Price:"13.70"},{ItemCode:"MED100",name:"复方氨酚咖敏口服溶液",SpecInfo:"100ml",Uom:"瓶",TarSubCate:"非处方药",Price:"15.90"}],i={name:"priceScreen",data(){return{loading:!0,videoError:!1,videoSrc:r("8639"),scrollTimer:null,scrollSpeed:.5,currentScrollTop:0,originalData:[],priceData:[],appendCount:0,maxAppendCount:5,minDataForScroll:10}},computed:{priceText(){return e=>{const t=a(e,4);return t?t+"元":"--"}}},created(){this.originalData=[...c],this.priceData=[...c],this.testFun()},mounted(){this.initPage()},methods:{async testFun(){const e=await this.$api.price.queryPrice();e.success?console.log("queryPrice查",e):(console.log("queryPrice查2",e),this.$message.error(e.message))},async initPage(){setTimeout(()=>{this.loading=!1,this.$nextTick(()=>{setTimeout(()=>{this.checkAndStartScrolling()},500)})},1e3)},checkAndStartScrolling(){const e=this.$refs.scrollingTable;e?this.$nextTick(()=>{const t=e.querySelector(".el-table__body-wrapper");if(!t)return void console.log("表格主体包装器tableBody未找到");const r=t.scrollHeight,n=t.clientHeight;console.log(`scrollHeight: ${r}, clientHeight: ${n}`),r>n?(console.log("内容需要滚动,开始滚动动画"),this.startScrolling()):(console.log("内容完全适配容器,无需滚动操作"),this.originalData.length<this.minDataForScroll&&(console.log("数据量太少,导致滚动时出现数据重复的情况"),this.duplicateDataForScrolling()))}):console.log("滚动表格的Ref未找到")},duplicateDataForScrolling(){const e=Math.max(this.minDataForScroll,3*this.originalData.length),t=[];while(t.length<e)t.push(...this.originalData);this.priceData=t.slice(0,e),this.appendCount=Math.floor(e/this.originalData.length)-1,this.$nextTick(()=>{this.startScrolling()})},startScrolling(){const e=this.$refs.scrollingTable;e?this.$nextTick(()=>{const t=e.querySelector(".el-table__body-wrapper");if(!t)return void console.log("表格主体包装器tableBody未找到");console.log("开始滚动动画");const r=()=>{this.currentScrollTop+=this.scrollSpeed;const e=t.scrollHeight,n=t.clientHeight,o=e-n;this.currentScrollTop>=o-100?this.appendDataForInfiniteScroll():(t.scrollTop=this.currentScrollTop,this.scrollTimer=requestAnimationFrame(r))};this.scrollTimer=requestAnimationFrame(r)}):console.log("滚动表格的Ref未找到")},appendDataForInfiniteScroll(){if(console.log("附加数据,当前附加次数: "+this.appendCount),this.appendCount>=this.maxAppendCount)return console.log("达到最大追加次数，正在清理数据"),void this.cleanupAndResetData();this.priceData=[...this.originalData,...this.priceData],this.appendCount++,this.$nextTick(()=>{const e=this.$refs.scrollingTable,t=e.querySelector(".el-table__body-wrapper");if(t){const e=t.scrollHeight/this.priceData.length,r=e*this.originalData.length;this.currentScrollTop+=r,t.scrollTop=this.currentScrollTop,console.log("数据已添加,新的滚动位置: "+this.currentScrollTop),this.startScrolling()}})},cleanupAndResetData(){console.log("清理数据并重置"),this.priceData=[...this.originalData,...this.originalData,...this.originalData],this.appendCount=2,this.currentScrollTop=0,this.$nextTick(()=>{this.startScrolling()})},clearScrollTimer(){this.scrollTimer&&(cancelAnimationFrame(this.scrollTimer),this.scrollTimer=null)},handleVideoError(){this.videoError=!0,console.error("视频加载失败")},handleVideoLoadStart(){this.videoError=!1},handleVideoCanPlay(){this.videoError=!1}},beforeDestroy(){this.clearScrollTimer()}},u=i,s=(r("d0fd"),r("2877")),m=Object(s["a"])(u,n,o,!1,null,"0a7e4a1c",null);t["default"]=m.exports},c04e:function(e,t,r){"use strict";var n=r("c65b"),o=r("861d"),a=r("d9b5"),c=r("dc4a"),i=r("485a"),u=r("b622"),s=TypeError,m=u("toPrimitive");e.exports=function(e,t){if(!o(e)||a(e))return e;var r,u=c(e,m);if(u){if(void 0===t&&(t="default"),r=n(u,e,t),!o(r)||a(r))return r;throw new s("Can't convert object to primitive value")}return void 0===t&&(t="number"),i(e,t)}},c430:function(e,t,r){"use strict";e.exports=!1},c65b:function(e,t,r){"use strict";var n=r("40d5"),o=Function.prototype.call;e.exports=n?o.bind(o):function(){return o.apply(o,arguments)}},c6b6:function(e,t,r){"use strict";var n=r("e330"),o=n({}.toString),a=n("".slice);e.exports=function(e){return a(o(e),8,-1)}},c6cd:function(e,t,r){"use strict";var n=r("c430"),o=r("cfe9"),a=r("6374"),c="__core-js_shared__",i=e.exports=o[c]||a(c,{});(i.versions||(i.versions=[])).push({version:"3.45.0",mode:n?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.45.0/LICENSE",source:"https://github.com/zloirock/core-js"})},ca84:function(e,t,r){"use strict";var n=r("e330"),o=r("1a2d"),a=r("fc6a"),c=r("4d64").indexOf,i=r("d012"),u=n([].push);e.exports=function(e,t){var r,n=a(e),s=0,m=[];for(r in n)!o(i,r)&&o(n,r)&&u(m,r);while(t.length>s)o(n,r=t[s++])&&(~c(m,r)||u(m,r));return m}},cb2d:function(e,t,r){"use strict";var n=r("1626"),o=r("9bf2"),a=r("13d2"),c=r("6374");e.exports=function(e,t,r,i){i||(i={});var u=i.enumerable,s=void 0!==i.name?i.name:t;if(n(r)&&a(r,s,i),i.global)u?e[t]=r:c(t,r);else{try{i.unsafe?e[t]&&(u=!0):delete e[t]}catch(m){}u?e[t]=r:o.f(e,t,{value:r,enumerable:!1,configurable:!i.nonConfigurable,writable:!i.nonWritable})}return e}},cc12:function(e,t,r){"use strict";var n=r("cfe9"),o=r("861d"),a=n.document,c=o(a)&&o(a.createElement);e.exports=function(e){return c?a.createElement(e):{}}},cdce:function(e,t,r){"use strict";var n=r("cfe9"),o=r("1626"),a=n.WeakMap;e.exports=o(a)&&/native code/.test(String(a))},cfe9:function(e,t,r){"use strict";(function(t){var r=function(e){return e&&e.Math===Math&&e};e.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof t&&t)||r("object"==typeof this&&this)||function(){return this}()||Function("return this")()}).call(this,r("c8ba"))},d012:function(e,t,r){"use strict";e.exports={}},d039:function(e,t,r){"use strict";e.exports=function(e){try{return!!e()}catch(t){return!0}}},d066:function(e,t,r){"use strict";var n=r("cfe9"),o=r("1626"),a=function(e){return o(e)?e:void 0};e.exports=function(e,t){return arguments.length<2?a(n[e]):n[e]&&n[e][t]}},d0fd:function(e,t,r){"use strict";r("198f")},d1e7:function(e,t,r){"use strict";var n={}.propertyIsEnumerable,o=Object.getOwnPropertyDescriptor,a=o&&!n.call({1:2},1);t.f=a?function(e){var t=o(this,e);return!!t&&t.enumerable}:n},d9b5:function(e,t,r){"use strict";var n=r("d066"),o=r("1626"),a=r("3a9b"),c=r("fdbf"),i=Object;e.exports=c?function(e){return"symbol"==typeof e}:function(e){var t=n("Symbol");return o(t)&&a(t.prototype,i(e))}},dc4a:function(e,t,r){"use strict";var n=r("59ed"),o=r("7234");e.exports=function(e,t){var r=e[t];return o(r)?void 0:n(r)}},e330:function(e,t,r){"use strict";var n=r("40d5"),o=Function.prototype,a=o.call,c=n&&o.bind.bind(a,a);e.exports=n?c:function(e){return function(){return a.apply(e,arguments)}}},e893:function(e,t,r){"use strict";var n=r("1a2d"),o=r("56ef"),a=r("06cf"),c=r("9bf2");e.exports=function(e,t,r){for(var i=o(t),u=c.f,s=a.f,m=0;m<i.length;m++){var f=i[m];n(e,f)||r&&n(r,f)||u(e,f,s(t,f))}}},e8b5:function(e,t,r){"use strict";var n=r("c6b6");e.exports=Array.isArray||function(e){return"Array"===n(e)}},f772:function(e,t,r){"use strict";var n=r("5692"),o=r("90e3"),a=n("keys");e.exports=function(e){return a[e]||(a[e]=o(e))}},fc6a:function(e,t,r){"use strict";var n=r("44ad"),o=r("1d80");e.exports=function(e){return n(o(e))}},fdbf:function(e,t,r){"use strict";var n=r("04f8");e.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator}}]);